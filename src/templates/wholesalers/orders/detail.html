{% extends 'wholesalers/base.html' %}

{% block title %}تفاصيل الطلب #{{ order.id }}{% endblock %}

{% block content %}
<!-- Print Header (Hidden by default, shown only in print) -->
<div class="print-header" style="display: none;">
    {% comment %} <div class="row mb-4"> {% endcomment %}
        <!-- Wholesaler Information -->
        {% comment %} <div class="col-6">
            <div class="print-company-info">
                {% if wholesaler.logo %}
                <img src="{{ wholesaler.logo.url }}" alt="{{ wholesaler.title }}" class="print-logo mb-2">
                {% endif %}
                <h3 class="print-company-name">{{ wholesaler.title }}</h3>
                <p class="print-company-details">
                    <strong>اسم المستخدم:</strong> {{ wholesaler.username }}<br>
                    <strong>الفئة:</strong>
                    {% if wholesaler.category == 'GROCERY' %}
                        بقالة
                    {% elif wholesaler.category == 'PHARMACEUTICAL' %}
                        صيدلية
                    {% elif wholesaler.category == 'ELECTRONICS' %}
                        إلكترونيات
                    {% else %}
                        {{ wholesaler.category }}
                    {% endif %}<br>
                    {% if wholesaler.user.phone %}
                    <strong>الهاتف:</strong> {{ wholesaler.user.phone }}<br>
                    {% endif %}
                    {% if wholesaler.user.email %}
                    <strong>البريد الإلكتروني:</strong> {{ wholesaler.user.email }}
                    {% endif %}
                </p>
            </div>
        </div>

        <!-- Store Information -->
        <div class="col-6">
            <div class="print-store-info">
                <h4 class="print-section-title">معلومات المتجر</h4>
                <div class="print-store-details">
                    <p><strong>اسم المتجر:</strong> {{ order.store.name }}</p>
                    <p><strong>صاحب المتجر:</strong> {{ order.store.owner.username }}</p>
                    {% if order.store.owner.phone %}
                    <p><strong>هاتف المتجر:</strong> {{ order.store.owner.phone }}</p>
                    {% endif %}
                    {% if order.store.owner.email %}
                    <p><strong>بريد المتجر:</strong> {{ order.store.owner.email }}</p>
                    {% endif %}
                    <p><strong>عنوان المتجر:</strong> {{ order.store.address }}</p>
                    {% if order.store.city %}
                    <p><strong>المدينة:</strong> {{ order.store.city.name }}</p>
                    {% endif %}
                    {% if order.store.state %}
                    <p><strong>المحافظة:</strong> {{ order.store.state.name }}</p>
                    {% endif %}
                    {% if order.store.country %}
                    <p><strong>الدولة:</strong> {{ order.store.country.name }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div> {% endcomment %}

    <!-- Invoice Header -->
    <div class="text-center mb-4 print-invoice-header">
        <h2 class="print-invoice-title">فاتورة طلب #{{ order.id }}</h2>
        <div class="print-invoice-dates">
            <span class="print-date-item">تاريخ الطلب: {{ order.created_at|date:"Y/m/d H:i" }}</span>
            <span class="print-date-separator">|</span>
            <span class="print-date-item">تاريخ الطباعة: <span id="printDate"></span></span>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-file-invoice me-2 text-success"></i>
                    تفاصيل الطلب #{{ order.id }}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'wholesaler_dashboard' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'orders_list' %}">الطلبات</a></li>
                        <li class="breadcrumb-item active">طلب #{{ order.id }}</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'order_print' order.id %}" target="_blank" class="btn btn-outline-secondary">
                    <i class="fas fa-print me-2"></i>
                    طباعة فاتورة
                </a>
                <a href="{% url 'orders_list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-semibold">رقم الطلب:</td>
                                <td>#{{ order.id }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">تاريخ الطلب:</td>
                                <td>{{ order.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">الحالة:</td>
                                <td>
                                    {% if order.status == 'pending' %}
                                        <span class="badge bg-warning">في الانتظار</span>
                                    {% elif order.status == 'processing' %}
                                        <span class="badge bg-info">قيد المعالجة</span>
                                    {% elif order.status == 'shipped' %}
                                        <span class="badge bg-primary">تم الشحن</span>
                                    {% elif order.status == 'delivered' %}
                                        <span class="badge bg-success">تم التسليم</span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">عدد المنتجات:</td>
                                <td>{{ order.order_items.count }} منتج</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            {% comment %} <tr>
                                <td class="fw-semibold">المبلغ الفرعي:</td>
                                <td>{{ order.products_total_price }} ج.م</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">الرسوم:</td>
                                <td>{{ order.fees }} ج.م</td>
                            </tr> {% endcomment %}
                            <tr>
                                <td class="fw-semibold">المبلغ الإجمالي:</td>
                                <td class="text-success fw-bold fs-5">{{ order.products_total_price }} ج.م</td>
                            </tr>
                            {% if order.deliver_at %}
                            <tr>
                                <td class="fw-semibold">موعد التسليم:</td>
                                <td>{{ order.deliver_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>



        <!-- Order Items -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    منتجات الطلب ({{ order_items.count }} منتج)
                </h5>
            </div>
            <div class="card-body p-0" id="orderItemsContainer">
                {% include 'wholesalers/orders/partials/order_items_table.html' %}
            </div>
        </div>

        <!-- Order Timeline -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ الطلب والتحديثات
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <!-- Order Created -->
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success">
                            <i class="fas fa-plus text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم إنشاء الطلب</h6>
                            <p class="text-muted mb-1">تم إنشاء الطلب بواسطة {{ order.store.name }}</p>
                            <small class="text-muted">{{ order.created_at|timesince }} مضت - {{ order.created_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>

                    <!-- Status Updates -->
                    {% if order.status != 'pending' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info">
                            <i class="fas fa-check text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم قبول الطلب</h6>
                            <p class="text-muted mb-1">تم قبول الطلب وبدء المعالجة</p>
                            <small class="text-muted">{{ order.updated_at|timesince }} مضت - {{ order.updated_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% if order.status == 'shipped' or order.status == 'delivered' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary">
                            <i class="fas fa-shipping-fast text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم شحن الطلب</h6>
                            <p class="text-muted mb-1">الطلب في الطريق إلى المتجر</p>
                            <small class="text-muted">{{ order.updated_at|timesince }} مضت - {{ order.updated_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% if order.status == 'delivered' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success">
                            <i class="fas fa-check-circle text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم تسليم الطلب</h6>
                            <p class="text-muted mb-1">تم تسليم الطلب بنجاح للمتجر</p>
                            <small class="text-muted">{{ order.updated_at|timesince }} مضت - {{ order.updated_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% if order.status == 'cancelled' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-danger">
                            <i class="fas fa-times text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">تم إلغاء الطلب</h6>
                            <p class="text-muted mb-1">تم إلغاء الطلب</p>
                            <small class="text-muted">{{ order.updated_at|timesince }} مضت - {{ order.updated_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Store Information & Actions -->
    <div class="col-lg-4">
        <!-- Store Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-store me-2"></i>
                    معلومات المتجر
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2">
                        <i class="fas fa-store fa-2x text-muted"></i>
                    </div>
                    <h6 class="mb-1">{{ order.store.name }}</h6>
                    <small class="text-muted">{{ order.store.owner.username }}</small>
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-semibold">الهاتف:</td>
                        <td>{{ order.store.owner.phone }}</td>
                    </tr>
                    {% if order.store.owner.email %}
                    <tr>
                        <td class="fw-semibold">البريد:</td>
                        <td>{{ order.store.owner.email }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="fw-semibold">العنوان:</td>
                        <td>{{ order.store.address }}</td>
                    </tr>
                    {% if order.store.city %}
                    <tr>
                        <td class="fw-semibold">المدينة:</td>
                        <td>{{ order.store.city.name }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- Order Status Progress -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    حالة الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="status-progress mb-3">
                    <div class="status-step {% if order.status == 'pending' or order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}active{% endif %}">
                        <div class="status-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <span>في الانتظار</span>
                    </div>
                    <div class="status-step {% if order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}active{% endif %}">
                        <div class="status-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span>قيد المعالجة</span>
                    </div>
                    <div class="status-step {% if order.status == 'shipped' or order.status == 'delivered' %}active{% endif %}">
                        <div class="status-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <span>تم الشحن</span>
                    </div>
                    <div class="status-step {% if order.status == 'delivered' %}active{% endif %}">
                        <div class="status-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <span>تم التسليم</span>
                    </div>
                </div>

                <!-- Current Status Badge -->
                <div class="text-center mb-3">
                    {% if order.status == 'pending' %}
                        <span class="badge bg-warning fs-6 px-3 py-2">في الانتظار</span>
                    {% elif order.status == 'processing' %}
                        <span class="badge bg-info fs-6 px-3 py-2">قيد المعالجة</span>
                    {% elif order.status == 'shipped' %}
                        <span class="badge bg-primary fs-6 px-3 py-2">تم الشحن</span>
                    {% elif order.status == 'delivered' %}
                        <span class="badge bg-success fs-6 px-3 py-2">تم التسليم</span>
                    {% elif order.status == 'cancelled' %}
                        <span class="badge bg-danger fs-6 px-3 py-2">ملغي</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Order Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إجراءات الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2" id="orderActions">
                    {% if order.status == 'pending' %}
                        <button class="btn btn-success" onclick="updateOrderStatus({{ order.id }}, 'processing', 'قبول الطلب')">
                            <i class="fas fa-check me-2"></i>
                            قبول الطلب
                        </button>
                        <button class="btn btn-outline-warning" onclick="editOrder()">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الطلب
                        </button>
                        <button class="btn btn-danger" onclick="updateOrderStatus({{ order.id }}, 'cancelled', 'إلغاء الطلب')">
                            <i class="fas fa-times me-2"></i>
                            إلغاء الطلب
                        </button>
                    {% elif order.status == 'processing' %}
                        <button class="btn btn-primary" onclick="updateOrderStatus({{ order.id }}, 'shipped', 'تأكيد الشحن')">
                            <i class="fas fa-shipping-fast me-2"></i>
                            تم الشحن
                        </button>
                        <button class="btn btn-outline-secondary" onclick="updateOrderStatus({{ order.id }}, 'pending', 'إرجاع للانتظار')">
                            <i class="fas fa-undo me-2"></i>
                            إرجاع للانتظار
                        </button>
                    {% elif order.status == 'shipped' %}
                        <button class="btn btn-success" onclick="updateOrderStatus({{ order.id }}, 'delivered', 'تأكيد التسليم')">
                            <i class="fas fa-check-circle me-2"></i>
                            تم التسليم
                        </button>
                        <button class="btn btn-outline-secondary" onclick="updateOrderStatus({{ order.id }}, 'processing', 'إرجاع للمعالجة')">
                            <i class="fas fa-undo me-2"></i>
                            إرجاع للمعالجة
                        </button>
                    {% elif order.status == 'delivered' %}
                        <div class="alert alert-success text-center mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            تم تسليم الطلب بنجاح
                        </div>
                    {% elif order.status == 'cancelled' %}
                        <div class="alert alert-danger text-center mb-0">
                            <i class="fas fa-times-circle me-2"></i>
                            تم إلغاء الطلب
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'order_print' order.id %}" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        طباعة فاتورة
                    </a>

                    <button class="btn btn-outline-info" onclick="contactStore()">
                        <i class="fas fa-phone me-2"></i>
                        الاتصال بالمتجر
                    </button>

                    <button class="btn btn-outline-success" onclick="sendWhatsApp()">
                        <i class="fab fa-whatsapp me-2"></i>
                        إرسال واتساب
                    </button>

                    <hr>

                    <button class="btn btn-outline-secondary" onclick="duplicateOrder()">
                        <i class="fas fa-copy me-2"></i>
                        نسخ الطلب
                    </button>

                    <button class="btn btn-outline-warning" onclick="exportOrder()">
                        <i class="fas fa-download me-2"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Remove Item Modal -->
{% include 'wholesalers/orders/partials/remove_item_modal.html' %}

{% endblock %}

{% block extra_css %}
<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-green), var(--secondary-green));
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2.5rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.timeline-content {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Status Progress Styles */
.status-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin: 2rem 0;
}

.status-progress::before {
    content: '';
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    right: 1.5rem;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
    flex: 1;
}

.status-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    border: 3px solid white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-step.active .status-icon {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    color: white;
}

.status-step span {
    font-size: 0.8rem;
    font-weight: 500;
    color: #6c757d;
}

.status-step.active span {
    color: var(--primary-green);
    font-weight: 600;
}

/* Print styles removed - now using dedicated print template */

/* Mobile Responsive */
@media (max-width: 768px) {
    .status-progress {
        flex-direction: column;
        gap: 1rem;
    }

    .status-progress::before {
        display: none;
    }

    .timeline {
        padding-left: 1.5rem;
    }

    .timeline-marker {
        left: -2rem;
        width: 1.5rem;
        height: 1.5rem;
    }

    .status-icon {
        width: 2.5rem;
        height: 2.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Status update with enhanced confirmation
function updateOrderStatus(orderId, newStatus, actionName) {
    const statusNames = {
        'pending': 'في الانتظار',
        'processing': 'قيد المعالجة',
        'shipped': 'تم الشحن',
        'delivered': 'تم التسليم',
        'cancelled': 'ملغي'
    };

    const confirmMessage = `هل أنت متأكد من ${actionName}؟\nسيتم تغيير حالة الطلب إلى "${statusNames[newStatus]}"`;

    if (confirm(confirmMessage)) {
        // Show loading state
        const actionButtons = document.getElementById('orderActions');
        const originalContent = actionButtons.innerHTML;
        actionButtons.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحديث...</span>
                </div>
                <p class="mt-2 mb-0">جاري تحديث حالة الطلب...</p>
            </div>
        `;

        const formData = new FormData();
        formData.append('status', newStatus);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        fetch(`/wholesaler/orders/${orderId}/update-status/`, {
            method: 'POST',
            body: formData,
            headers: {
                'HX-Request': 'true'
            }
        })
        .then(response => {
            if (response.ok) {
                // Show success message
                actionButtons.innerHTML = `
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تحديث حالة الطلب بنجاح
                    </div>
                `;

                // Reload page after 1.5 seconds
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                throw new Error('فشل في تحديث الحالة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            actionButtons.innerHTML = originalContent;
            alert('حدث خطأ أثناء تحديث حالة الطلب. يرجى المحاولة مرة أخرى.');
        });
    }
}

// Print functionality moved to dedicated print template

// Contact store functionality
function contactStore() {
    const phone = '{{ order.store.owner.phone }}';
    if (phone) {
        if (confirm(`هل تريد الاتصال بـ {{ order.store.name }}؟\nرقم الهاتف: ${phone}`)) {
            window.open(`tel:${phone}`, '_self');
        }
    } else {
        alert('رقم الهاتف غير متوفر');
    }
}

// WhatsApp functionality
function sendWhatsApp() {
    const phone = '{{ order.store.owner.phone }}';
    if (phone) {
        const message = `مرحباً {{ order.store.name }},\n\nبخصوص طلبكم رقم #{{ order.id }}\nالمبلغ الإجمالي: {{ order.total_price }} ج.م\n\nشكراً لتعاملكم معنا\n{{ wholesaler.title }}`;
        const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    } else {
        alert('رقم الهاتف غير متوفر');
    }
}

// Edit order functionality
function editOrder() {
    if (confirm('هل تريد تعديل هذا الطلب؟\nسيتم توجيهك إلى صفحة التعديل.')) {
        // Placeholder for edit functionality
        alert('سيتم تنفيذ ميزة تعديل الطلب قريباً');
    }
}

// Duplicate order functionality
function duplicateOrder() {
    if (confirm('هل تريد إنشاء طلب جديد بنفس المنتجات؟')) {
        // Placeholder for duplicate functionality
        alert('سيتم تنفيذ ميزة نسخ الطلب قريباً');
    }
}

// Export order functionality
function exportOrder() {
    const exportOptions = [
        'تصدير كـ PDF',
        'تصدير كـ Excel',
        'تصدير كـ CSV'
    ];

    const choice = prompt(`اختر نوع التصدير:\n${exportOptions.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\nأدخل رقم الخيار (1-3):`);

    if (choice && choice >= 1 && choice <= 3) {
        alert(`سيتم تنفيذ ${exportOptions[choice - 1]} قريباً`);
    }
}

// Auto-refresh for pending orders
document.addEventListener('DOMContentLoaded', function() {
    const orderStatus = '{{ order.status }}';

    // Auto-refresh every 30 seconds for pending/processing orders
    if (orderStatus === 'pending' || orderStatus === 'processing') {
        setInterval(function() {
            // Check if page is visible
            if (!document.hidden) {
                fetch(window.location.href, {
                    headers: {
                        'HX-Request': 'true'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Check if status has changed by looking for status badge
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newStatusBadge = doc.querySelector('.badge.fs-6');
                    const currentStatusBadge = document.querySelector('.badge.fs-6');

                    if (newStatusBadge && currentStatusBadge &&
                        newStatusBadge.textContent !== currentStatusBadge.textContent) {
                        // Status changed, reload page
                        location.reload();
                    }
                })
                .catch(error => {
                    console.log('Auto-refresh failed:', error);
                });
            }
        }, 30000); // 30 seconds
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+P for print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printOrder();
        }

        // Ctrl+E for edit (if order is pending)
        if (e.ctrlKey && e.key === 'e' && orderStatus === 'pending') {
            e.preventDefault();
            editOrder();
        }
    });
});

// Remove Item Modal Functions
let currentOrderId = null;
let currentItemId = null;

function showRemoveItemModal(orderId, itemId, productName) {
    currentOrderId = orderId;
    currentItemId = itemId;

    // Set product name in modal
    document.getElementById('productNameToRemove').textContent = productName;

    // Reset form
    document.getElementById('removeItemForm').reset();
    document.getElementById('customReasonDiv').style.display = 'none';
    document.getElementById('customReason').required = false;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('removeItemModal'));
    modal.show();
}

function confirmRemoveItem() {
    if (!currentOrderId || !currentItemId) {
        alert('حدث خطأ في البيانات. يرجى المحاولة مرة أخرى.');
        return;
    }

    const form = document.getElementById('removeItemForm');
    const reasonSelect = document.getElementById('removalReason');
    const customReason = document.getElementById('customReason');

    // Validate form
    if (!reasonSelect.value) {
        alert('يرجى اختيار سبب الحذف');
        return;
    }

    if (reasonSelect.value === 'أخرى' && !customReason.value.trim()) {
        alert('يرجى كتابة السبب المخصص');
        return;
    }

    // Prepare form data
    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

    if (reasonSelect.value === 'أخرى') {
        formData.append('reason', customReason.value.trim());
    } else {
        formData.append('reason', reasonSelect.value);
    }

    // Show loading state
    const confirmButton = document.querySelector('#removeItemModal .btn-danger');
    const originalText = confirmButton.innerHTML;
    confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحذف...';
    confirmButton.disabled = true;

    // Send request
    fetch(`/wholesaler/orders/${currentOrderId}/remove-item/${currentItemId}/`, {
        method: 'POST',
        body: formData,
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.text();
        } else {
            throw new Error('فشل في حذف المنتج');
        }
    })
    .then(html => {
        // Update the order items container
        document.getElementById('orderItemsContainer').innerHTML = html;

        // Hide modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('removeItemModal'));
        modal.hide();

        // Show success message
        showSuccessMessage('تم حذف المنتج بنجاح وتم استرداد الكمية إلى المخزون');

        // Reload page after 2 seconds to update all totals
        setTimeout(() => {
            location.reload();
        }, 2000);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        // Restore button state
        confirmButton.innerHTML = originalText;
        confirmButton.disabled = false;
    });
}

function showSuccessMessage(message) {
    // Create success alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
